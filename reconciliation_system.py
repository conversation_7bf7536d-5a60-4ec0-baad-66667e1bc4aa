"""
Reconciliation Automation System
Automates the creation of Console reconciliation sheets from trader and broker Excel files.
"""

import pandas as pd
import numpy as np
from datetime import datetime
import re
import os
import json
from typing import Dict, List, Tuple, Optional, Union
import warnings
warnings.filterwarnings('ignore')


class DataParser:
    """Handles parsing and normalization of trader and broker files."""
    
    def __init__(self):
        self.broker_column_mapping = {
            'tradedDateTime': 'Date',
            'positionholderid': 'Account', 
            'productid': 'Contract',
            'prompt': 'Expiry',
            'price': 'Price',
            'originallots': 'Lot',
            'contractsize': 'Contract',
            'multiplicationfactor': 'multiplicationfactor',
            'contractvalue': 'Contract Value',
            'commissionamount': 'Commission',
            'inputSource': 'inputSource',
            'Venue_Identification': 'Venue',
            'closingprice': 'Closing Price',
            'profitloss': 'profitloss'
        }
        
        self.trader_column_mapping = {
            # Star trader files
            'Exchange': 'Venue',
            'Commodity': 'Contract', 
            'Buy/Sell': 'Side',
            'Leg': 'Expiry',
            'Leg Volume': 'Lot',
            'Leg Price': 'Price',
            
            # CQG trader files  
            'Symbol': 'Contract',
            'B/S': 'Side',
            'Size': 'Lot',
            'Avg Fill Price': 'Price',
            'Place Time': 'Date',
            'Account': 'Account',
            'Exchange': 'Venue',
            'Expiration': 'Expiry',
            'Status': 'Status'
        }

    def clean_numeric_value(self, value: Union[str, float, int]) -> float:
        """Clean and convert numeric values, handling commas and text."""
        if pd.isna(value):
            return 0.0
        
        if isinstance(value, (int, float)):
            return float(value)
            
        if isinstance(value, str):
            # Remove commas and whitespace
            cleaned = re.sub(r'[,\s]', '', value)
            # Extract numeric part
            numeric_match = re.search(r'-?\d+\.?\d*', cleaned)
            if numeric_match:
                return float(numeric_match.group())
        
        return 0.0

    def clean_date_value(self, value: Union[str, datetime, pd.Timestamp]) -> Optional[datetime]:
        """Clean and standardize date values."""
        if pd.isna(value):
            return None
            
        if isinstance(value, (datetime, pd.Timestamp)):
            return pd.to_datetime(value)
            
        if isinstance(value, str):
            # Try different date formats
            date_formats = [
                '%d/%m/%Y %H:%M:%S',
                '%d/%m/%Y',
                '%Y-%m-%d %H:%M:%S', 
                '%Y-%m-%d',
                '%d-%b',
                '%d-%b-%Y',
                '%d %B %Y'
            ]
            
            for fmt in date_formats:
                try:
                    return pd.to_datetime(value, format=fmt)
                except:
                    continue
                    
            # Try pandas auto-parsing as last resort
            try:
                return pd.to_datetime(value)
            except:
                return None
        
        return None

    def parse_broker_file(self, file_path: str) -> pd.DataFrame:
        """Parse broker Excel file and return normalized DataFrame."""
        try:
            df = pd.read_excel(file_path)
            
            # Apply column mapping
            df_mapped = pd.DataFrame()
            for broker_col, console_col in self.broker_column_mapping.items():
                if broker_col in df.columns:
                    df_mapped[console_col] = df[broker_col]
            
            # Clean and normalize data
            if 'Date' in df_mapped.columns:
                df_mapped['Date'] = df_mapped['Date'].apply(self.clean_date_value)
            
            numeric_columns = ['Price', 'Lot', 'Contract Value', 'Commission', 'Closing Price', 'profitloss', 'multiplicationfactor']
            for col in numeric_columns:
                if col in df_mapped.columns:
                    df_mapped[col] = df_mapped[col].apply(self.clean_numeric_value)
            
            # Add data source identifier
            df_mapped['Data Type'] = 'Broker'
            
            return df_mapped
            
        except Exception as e:
            print(f"Error parsing broker file {file_path}: {e}")
            return pd.DataFrame()

    def parse_star_trader_file(self, file_path: str) -> pd.DataFrame:
        """Parse Star trader Excel file format."""
        try:
            # Read raw data to find the actual data header row
            df_raw = pd.read_excel(file_path, header=None)

            # Find the row with actual column headers (Exchange, Commodity, Buy/Sell, etc.)
            header_row = None
            for i, row in df_raw.iterrows():
                row_values = [str(cell) for cell in row if pd.notna(cell)]
                # Look for the actual data header row, not the description row
                if len(row_values) >= 4 and 'Exchange' in row_values and 'Commodity' in row_values:
                    # Make sure this is the actual header with individual column names
                    if any('Buy/Sell' in str(cell) or 'Leg Volume' in str(cell) for cell in row):
                        header_row = i
                        break

            if header_row is None:
                print(f"Could not find data header row in {file_path}")
                return pd.DataFrame()

            # Read data starting from header row
            df = pd.read_excel(file_path, skiprows=header_row)

            # Remove empty rows and rows with no meaningful data
            df = df.dropna(how='all')
            df = df[df.iloc[:, 0].notna()]  # Remove rows where first column is empty
            
            # Apply column mapping
            df_mapped = pd.DataFrame()
            for trader_col, console_col in self.trader_column_mapping.items():
                if trader_col in df.columns:
                    df_mapped[console_col] = df[trader_col]
            
            # Clean numeric data
            numeric_columns = ['Lot', 'Price']
            for col in numeric_columns:
                if col in df_mapped.columns:
                    df_mapped[col] = df_mapped[col].apply(self.clean_numeric_value)
            
            # Handle Buy/Sell conversion to lot signs
            if 'Side' in df_mapped.columns and 'Lot' in df_mapped.columns:
                df_mapped['Lot'] = df_mapped.apply(
                    lambda row: -abs(row['Lot']) if 'Sell' in str(row['Side']) else abs(row['Lot']), 
                    axis=1
                )
            
            # Add data source identifier
            df_mapped['Data Type'] = 'Trader'
            df_mapped['inputSource'] = 'Star'
            
            return df_mapped
            
        except Exception as e:
            print(f"Error parsing Star trader file {file_path}: {e}")
            return pd.DataFrame()

    def parse_cqg_trader_file(self, file_path: str) -> pd.DataFrame:
        """Parse CQG trader Excel file format."""
        try:
            # CQG files typically have headers in first row, but let's check
            df = pd.read_excel(file_path)

            # If first row doesn't look like data, it might be headers
            if df.shape[0] > 0:
                first_row = df.iloc[0]
                # Check if first row contains header-like text
                if any('Size' in str(cell) or 'Symbol' in str(cell) or 'B/S' in str(cell) for cell in first_row):
                    # Use first row as headers
                    df.columns = df.iloc[0]
                    df = df.drop(df.index[0]).reset_index(drop=True)

            # Clean column names (remove extra spaces)
            df.columns = [col.strip() if isinstance(col, str) else col for col in df.columns]
            
            # Apply column mapping
            df_mapped = pd.DataFrame()
            for trader_col, console_col in self.trader_column_mapping.items():
                if trader_col in df.columns:
                    df_mapped[console_col] = df[trader_col]
            
            # Clean date data
            if 'Date' in df_mapped.columns:
                df_mapped['Date'] = df_mapped['Date'].apply(self.clean_date_value)
            
            # Clean numeric data
            numeric_columns = ['Lot', 'Price']
            for col in numeric_columns:
                if col in df_mapped.columns:
                    df_mapped[col] = df_mapped[col].apply(self.clean_numeric_value)
            
            # Handle Buy/Sell conversion to lot signs
            if 'Side' in df_mapped.columns and 'Lot' in df_mapped.columns:
                df_mapped['Lot'] = df_mapped.apply(
                    lambda row: -abs(row['Lot']) if 'Sell' in str(row['Side']) else abs(row['Lot']), 
                    axis=1
                )
            
            # Filter only filled trades
            if 'Status' in df_mapped.columns:
                df_mapped = df_mapped[df_mapped['Status'].str.contains('Filled', na=False)]
            
            # Add data source identifier
            df_mapped['Data Type'] = 'Trader'
            df_mapped['inputSource'] = 'CQG'
            
            return df_mapped
            
        except Exception as e:
            print(f"Error parsing CQG trader file {file_path}: {e}")
            return pd.DataFrame()

    def parse_trader_file(self, file_path: str) -> pd.DataFrame:
        """Auto-detect trader file type and parse accordingly."""
        try:
            # Try to determine file type by examining content
            df_sample = pd.read_excel(file_path, nrows=5, header=None)
            content_str = str(df_sample.values).lower()
            
            if 'strategy trades' in content_str or 'exchange' in content_str:
                return self.parse_star_trader_file(file_path)
            elif 'symbol' in content_str or 'b/s' in content_str:
                return self.parse_cqg_trader_file(file_path)
            else:
                # Default to CQG format
                return self.parse_cqg_trader_file(file_path)
                
        except Exception as e:
            print(f"Error auto-detecting trader file type {file_path}: {e}")
            return pd.DataFrame()


class ConsoleGenerator:
    """Generates the final Console reconciliation sheet."""

    def __init__(self, config_file: str = None):
        # Define the exact Console column structure
        self.console_columns = [
            'Date', 'Account', 'Contract', 'Expiry', 'Price', 'Lot', 'Contract.1',
            'multiplicationfactor', 'Contract Value', 'Commission', 'inputSource',
            'Venue', 'Closing Price', 'Closing Contract Value', 'profitloss', 'Data Type',
            'Date.1', 'Account.1', 'Contract.2', 'Expiry.1', 'Price.1', 'Lot.1',
            'Contract.3', 'multiplicationfactor.1', 'Contract Value.1', 'Commission.1',
            'inputSource.1', 'Venue.1', 'Closing Price.1', 'Closing Contract Value.1',
            'profitloss.1', 'Rev Price', 'Rev Qty', 'Rev Value'
        ]

        # Initialize dynamic mappings
        self.contract_sizes = {}
        self.multiplication_factors = {}

        # Load configuration if provided
        if config_file and os.path.exists(config_file):
            self.load_config(config_file)
        else:
            # Fallback to default values only if no config
            self._set_default_values()

    def load_config(self, config_file: str):
        """Load contract sizes and multiplication factors from configuration file."""
        try:
            import json
            with open(config_file, 'r') as f:
                config = json.load(f)

            self.contract_sizes = config.get('contract_sizes', {})
            self.multiplication_factors = config.get('multiplication_factors', {})
            print(f"Loaded configuration from {config_file}")

        except Exception as e:
            print(f"Error loading config file {config_file}: {e}")
            self._set_default_values()

    def _set_default_values(self):
        """Set minimal default values - should be overridden by data or config."""
        self.contract_sizes = {
            'XAU': 5000, 'XAG': 5000, 'LCU': 25, 'CA': 25, 'AL': 25,
            'ZN': 25, 'PB': 25, 'SN': 5, 'NI': 6
        }
        self.multiplication_factors = {
            'XCEC': 0.01, 'XLME': 1.00, 'LME': 1.00
        }

    def extract_values_from_broker_data(self, broker_df: pd.DataFrame):
        """Extract contract sizes and multiplication factors directly from broker data."""
        if broker_df.empty:
            return

        print("🔍 Extracting dynamic values from broker data...")

        # Extract contract sizes from broker data if available
        if 'contractsize' in broker_df.columns and 'Contract' in broker_df.columns:
            for _, row in broker_df.iterrows():
                contract = str(row.get('Contract', '')).upper()
                contract_size = row.get('contractsize')

                if pd.notna(contract_size) and contract:
                    self.contract_sizes[contract] = float(contract_size)
                    print(f"  📊 Found contract size: {contract} = {contract_size}")

        # Extract multiplication factors from broker data if available
        if 'multiplicationfactor' in broker_df.columns and 'Venue' in broker_df.columns:
            for _, row in broker_df.iterrows():
                venue = str(row.get('Venue', '')).upper()
                mult_factor = row.get('multiplicationfactor')

                if pd.notna(mult_factor) and venue:
                    self.multiplication_factors[venue] = float(mult_factor)
                    print(f"  🏢 Found multiplication factor: {venue} = {mult_factor}")

    def learn_from_reference_console(self, reference_file: str = "Recon For reference.xlsx"):
        """Learn contract sizes and multiplication factors from reference Console."""
        try:
            if not os.path.exists(reference_file):
                print(f"⚠️ Reference file not found: {reference_file}")
                return

            print(f"📚 Learning from reference Console: {reference_file}")
            ref_df = pd.read_excel(reference_file, sheet_name='Console')

            # Extract unique combinations of Contract, Contract.1 (size), and multiplicationfactor
            if all(col in ref_df.columns for col in ['Contract', 'Contract.1', 'multiplicationfactor', 'Venue']):

                # Learn contract sizes
                contract_size_data = ref_df[['Contract', 'Contract.1']].dropna().drop_duplicates()
                for _, row in contract_size_data.iterrows():
                    contract = str(row['Contract']).upper()
                    size = row['Contract.1']
                    if pd.notna(size):
                        self.contract_sizes[contract] = float(size)
                        print(f"  📊 Learned contract size: {contract} = {size}")

                # Learn multiplication factors
                mult_factor_data = ref_df[['Venue', 'multiplicationfactor']].dropna().drop_duplicates()
                for _, row in mult_factor_data.iterrows():
                    venue = str(row['Venue']).upper()
                    factor = row['multiplicationfactor']
                    if pd.notna(factor):
                        self.multiplication_factors[venue] = float(factor)
                        print(f"  🏢 Learned multiplication factor: {venue} = {factor}")

        except Exception as e:
            print(f"⚠️ Error learning from reference Console: {e}")

    def save_learned_config(self, config_file: str = "learned_config.json"):
        """Save the learned contract sizes and multiplication factors to a config file."""
        try:
            config = {
                'contract_sizes': self.contract_sizes,
                'multiplication_factors': self.multiplication_factors,
                'last_updated': datetime.now().isoformat()
            }

            with open(config_file, 'w') as f:
                json.dump(config, f, indent=2)

            print(f"💾 Saved learned configuration to: {config_file}")

        except Exception as e:
            print(f"⚠️ Error saving config: {e}")

    def get_contract_size(self, contract: str) -> float:
        """Get contract size for a given instrument."""
        if pd.isna(contract):
            return 1.0

        contract_str = str(contract).upper()

        # Check for exact matches first
        for key, size in self.contract_sizes.items():
            if key in contract_str:
                return size

        # Default contract size
        return 1.0

    def get_multiplication_factor(self, venue: str, contract: str) -> float:
        """Get multiplication factor based on venue and contract."""
        if pd.isna(venue):
            return 1.0

        venue_str = str(venue).upper()

        # Check venue-specific factors
        for key, factor in self.multiplication_factors.items():
            if key in venue_str:
                return factor

        return 1.0

    def calculate_contract_value(self, price: float, lot: float, contract_size: float, mult_factor: float) -> float:
        """Calculate contract value: Lot × Price × Contract Size × Multiplication Factor."""
        try:
            return float(lot) * float(price) * float(contract_size) * float(mult_factor)
        except:
            return 0.0

    def calculate_closing_contract_value(self, closing_price: float, lot: float, contract_size: float, mult_factor: float) -> float:
        """Calculate closing contract value."""
        try:
            return float(lot) * float(closing_price) * float(contract_size) * float(mult_factor)
        except:
            return 0.0

    def calculate_profit_loss(self, closing_contract_value: float, contract_value: float) -> float:
        """Calculate profit/loss: Closing Contract Value - Contract Value."""
        try:
            return float(closing_contract_value) - float(contract_value)
        except:
            return 0.0

    def enrich_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add calculated fields and normalize data."""
        df_enriched = df.copy()

        # Ensure all required columns exist
        required_cols = ['Date', 'Account', 'Contract', 'Expiry', 'Price', 'Lot', 'Venue', 'Closing Price']
        for col in required_cols:
            if col not in df_enriched.columns:
                df_enriched[col] = None

        # Calculate contract sizes
        df_enriched['Contract.1'] = df_enriched['Contract'].apply(self.get_contract_size)

        # Calculate multiplication factors
        df_enriched['multiplicationfactor'] = df_enriched.apply(
            lambda row: self.get_multiplication_factor(row.get('Venue'), row.get('Contract')), axis=1
        )

        # Calculate contract values
        df_enriched['Contract Value'] = df_enriched.apply(
            lambda row: self.calculate_contract_value(
                row.get('Price', 0), row.get('Lot', 0),
                row.get('Contract.1', 1), row.get('multiplicationfactor', 1)
            ), axis=1
        )

        # Calculate closing contract values
        df_enriched['Closing Contract Value'] = df_enriched.apply(
            lambda row: self.calculate_closing_contract_value(
                row.get('Closing Price', 0), row.get('Lot', 0),
                row.get('Contract.1', 1), row.get('multiplicationfactor', 1)
            ), axis=1
        )

        # Calculate profit/loss
        df_enriched['profitloss'] = df_enriched.apply(
            lambda row: self.calculate_profit_loss(
                row.get('Closing Contract Value', 0), row.get('Contract Value', 0)
            ), axis=1
        )

        # Add default values for missing fields
        if 'Commission' not in df_enriched.columns:
            df_enriched['Commission'] = 0.0

        # Add Rev fields (these appear to be duplicates of main fields)
        df_enriched['Rev Price'] = df_enriched['Price']
        df_enriched['Rev Qty'] = df_enriched['Lot']
        df_enriched['Rev Value'] = df_enriched['Contract Value']

        return df_enriched

    def create_console_dataframe(self, broker_df: pd.DataFrame, trader_dfs: List[pd.DataFrame]) -> pd.DataFrame:
        """Create the final Console DataFrame by combining broker and trader data."""

        # Combine all trader data
        if trader_dfs:
            combined_trader_df = pd.concat(trader_dfs, ignore_index=True)
        else:
            combined_trader_df = pd.DataFrame()

        # Enrich both datasets
        if not broker_df.empty:
            broker_enriched = self.enrich_dataframe(broker_df)
        else:
            broker_enriched = pd.DataFrame()

        if not combined_trader_df.empty:
            trader_enriched = self.enrich_dataframe(combined_trader_df)
        else:
            trader_enriched = pd.DataFrame()

        # Create the Console structure - the pattern seems to duplicate data in two sets of columns
        console_rows = []

        # Process broker data
        if not broker_enriched.empty:
            for _, row in broker_enriched.iterrows():
                console_row = {}

                # First set of columns (main data)
                console_row['Date'] = row.get('Date')
                console_row['Account'] = row.get('Account')
                console_row['Contract'] = row.get('Contract')
                console_row['Expiry'] = row.get('Expiry')
                console_row['Price'] = row.get('Price')
                console_row['Lot'] = row.get('Lot')
                console_row['Contract.1'] = row.get('Contract.1')
                console_row['multiplicationfactor'] = row.get('multiplicationfactor')
                console_row['Contract Value'] = row.get('Contract Value')
                console_row['Commission'] = row.get('Commission')
                console_row['inputSource'] = row.get('inputSource')
                console_row['Venue'] = row.get('Venue')
                console_row['Closing Price'] = row.get('Closing Price')
                console_row['Closing Contract Value'] = row.get('Closing Contract Value')
                console_row['profitloss'] = row.get('profitloss')
                console_row['Data Type'] = row.get('Data Type')

                # Second set of columns (duplicate data)
                console_row['Date.1'] = row.get('Date')
                console_row['Account.1'] = row.get('Account')
                console_row['Contract.2'] = row.get('Contract')
                console_row['Expiry.1'] = row.get('Expiry')
                console_row['Price.1'] = row.get('Price')
                console_row['Lot.1'] = row.get('Lot')
                console_row['Contract.3'] = row.get('Contract.1')
                console_row['multiplicationfactor.1'] = row.get('multiplicationfactor')
                console_row['Contract Value.1'] = row.get('Contract Value')
                console_row['Commission.1'] = row.get('Commission')
                console_row['inputSource.1'] = row.get('inputSource')
                console_row['Venue.1'] = row.get('Venue')
                console_row['Closing Price.1'] = row.get('Closing Price')
                console_row['Closing Contract Value.1'] = row.get('Closing Contract Value')
                console_row['profitloss.1'] = row.get('profitloss')

                # Rev fields
                console_row['Rev Price'] = row.get('Rev Price')
                console_row['Rev Qty'] = row.get('Rev Qty')
                console_row['Rev Value'] = row.get('Rev Value')

                console_rows.append(console_row)

        # Process trader data (if needed for reconciliation)
        if not trader_enriched.empty:
            for _, row in trader_enriched.iterrows():
                console_row = {}

                # First set of columns (main data)
                console_row['Date'] = row.get('Date')
                console_row['Account'] = row.get('Account')
                console_row['Contract'] = row.get('Contract')
                console_row['Expiry'] = row.get('Expiry')
                console_row['Price'] = row.get('Price')
                console_row['Lot'] = row.get('Lot')
                console_row['Contract.1'] = row.get('Contract.1')
                console_row['multiplicationfactor'] = row.get('multiplicationfactor')
                console_row['Contract Value'] = row.get('Contract Value')
                console_row['Commission'] = row.get('Commission', 0.0)
                console_row['inputSource'] = row.get('inputSource')
                console_row['Venue'] = row.get('Venue')
                console_row['Closing Price'] = row.get('Closing Price', 0.0)
                console_row['Closing Contract Value'] = row.get('Closing Contract Value', 0.0)
                console_row['profitloss'] = row.get('profitloss', 0.0)
                console_row['Data Type'] = row.get('Data Type')

                # Second set of columns (duplicate data)
                console_row['Date.1'] = row.get('Date')
                console_row['Account.1'] = row.get('Account')
                console_row['Contract.2'] = row.get('Contract')
                console_row['Expiry.1'] = row.get('Expiry')
                console_row['Price.1'] = row.get('Price')
                console_row['Lot.1'] = row.get('Lot')
                console_row['Contract.3'] = row.get('Contract.1')
                console_row['multiplicationfactor.1'] = row.get('multiplicationfactor')
                console_row['Contract Value.1'] = row.get('Contract Value')
                console_row['Commission.1'] = row.get('Commission', 0.0)
                console_row['inputSource.1'] = row.get('inputSource')
                console_row['Venue.1'] = row.get('Venue')
                console_row['Closing Price.1'] = row.get('Closing Price', 0.0)
                console_row['Closing Contract Value.1'] = row.get('Closing Contract Value', 0.0)
                console_row['profitloss.1'] = row.get('profitloss', 0.0)

                # Rev fields
                console_row['Rev Price'] = row.get('Rev Price')
                console_row['Rev Qty'] = row.get('Rev Qty')
                console_row['Rev Value'] = row.get('Rev Value')

                console_rows.append(console_row)

        # Create final DataFrame
        console_df = pd.DataFrame(console_rows)

        # Ensure all Console columns are present in correct order
        for col in self.console_columns:
            if col not in console_df.columns:
                console_df[col] = None

        # Reorder columns to match Console structure
        console_df = console_df[self.console_columns]

        return console_df


class ReconciliationSystem:
    """Main reconciliation system that orchestrates the entire process."""

    def __init__(self, config_file: str = None, learn_from_reference: bool = True):
        self.parser = DataParser()
        self.generator = ConsoleGenerator(config_file)

        # Try to learn from reference Console if available
        if learn_from_reference:
            self.generator.learn_from_reference_console()

    def process_files(self, broker_file_path: str, trader_file_paths: List[str]) -> pd.DataFrame:
        """Process broker and trader files to generate Console reconciliation."""

        print("🚀 Starting reconciliation process...")

        # Parse broker file
        print(f"📄 Processing broker file: {broker_file_path}")
        broker_df = self.parser.parse_broker_file(broker_file_path)
        print(f"✅ Broker data: {len(broker_df)} rows")

        # Extract dynamic values from broker data
        if not broker_df.empty:
            self.generator.extract_values_from_broker_data(broker_df)

        # Parse trader files
        trader_dfs = []
        for trader_file in trader_file_paths:
            print(f"📄 Processing trader file: {trader_file}")
            trader_df = self.parser.parse_trader_file(trader_file)
            if not trader_df.empty:
                trader_dfs.append(trader_df)
                print(f"✅ Trader data: {len(trader_df)} rows")
            else:
                print(f"⚠️ Warning: No data found in {trader_file}")

        # Save learned configuration for future use
        self.generator.save_learned_config()

        # Generate Console
        print("🔧 Generating Console reconciliation...")
        console_df = self.generator.create_console_dataframe(broker_df, trader_dfs)
        print(f"✅ Console generated: {len(console_df)} rows")

        return console_df

    def save_console(self, console_df: pd.DataFrame, output_path: str = "Console_Output.xlsx"):
        """Save the Console DataFrame to Excel file."""
        try:
            console_df.to_excel(output_path, index=False, sheet_name='Console')
            print(f"Console saved to: {output_path}")
            return True
        except Exception as e:
            print(f"Error saving Console: {e}")
            return False


def main():
    """Main function for command-line usage."""
    import sys
    import os

    if len(sys.argv) < 3:
        print("Usage: python reconciliation_system.py <broker_file> <trader_file1> [trader_file2] ...")
        print("Example: python reconciliation_system.py 'Broker File.xlsx' 'Star AGL07 Trade File - 24 Apr.xlsx' 'CQG Trader.xlsx'")
        return

    broker_file = sys.argv[1]
    trader_files = sys.argv[2:]

    # Validate files exist
    if not os.path.exists(broker_file):
        print(f"Error: Broker file not found: {broker_file}")
        return

    for trader_file in trader_files:
        if not os.path.exists(trader_file):
            print(f"Error: Trader file not found: {trader_file}")
            return

    # Run reconciliation
    system = ReconciliationSystem()
    console_df = system.process_files(broker_file, trader_files)

    if not console_df.empty:
        # Generate output filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"Console_Reconciliation_{timestamp}.xlsx"

        if system.save_console(console_df, output_file):
            print(f"\n✅ Reconciliation completed successfully!")
            print(f"📄 Output file: {output_file}")
            print(f"📊 Total records: {len(console_df)}")
        else:
            print("\n❌ Error saving reconciliation file")
    else:
        print("\n❌ No data to reconcile")


if __name__ == "__main__":
    main()
