# 📊 Reconciliation Automation System

An automated system for creating Console reconciliation sheets from trader and broker Excel files.

## 🎯 Overview

This system automates the manual process of creating reconciliation sheets by:
- Parsing broker and trader Excel files with different formats
- Cleaning and normalizing data (dates, numbers, text)
- Applying business logic and calculations
- Generating a standardized Console output with 34 columns

## 📁 File Structure

```
reconciliation_system.py    # Main reconciliation engine
streamlit_app.py           # Web interface for file uploads
requirements.txt           # Python dependencies
README.md                  # This file
```

## 🚀 Quick Start

### Option 1: Command Line Interface

1. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Run reconciliation:**
   ```bash
   python reconciliation_system.py "Broker File.xlsx" "Trader1.xlsx" "Trader2.xlsx"
   ```

### Option 2: Web Interface (Recommended)

1. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Launch web interface:**
   ```bash
   streamlit run streamlit_app.py
   ```

3. **Open browser** and navigate to the displayed URL (usually http://localhost:8501)

4. **Upload files** and generate Console reconciliation

## 📋 Supported File Formats

### Broker Files
- Standard broker Excel format with columns like:
  - `tradedDateTime`, `positionholderid`, `productid`, `price`, `originallots`
  - `commissionamount`, `closingprice`, `contractvalue`, etc.

### Trader Files
- **Star Trader Format**: Files with "Strategy Trades Summary" structure
- **CQG Trader Format**: Files with order/trade data columns
- Auto-detection of file format based on content

## 🔧 System Features

### Data Processing
- **Date Normalization**: Handles multiple date formats (DD/MM/YYYY, DD-MMM, etc.)
- **Numeric Cleaning**: Removes commas, handles negative values
- **Column Mapping**: Maps different column names to standard Console format
- **Buy/Sell Logic**: Converts Buy/Sell indicators to positive/negative lot values

### Calculations
- **Contract Value**: `Lot × Price × Contract Size × Multiplication Factor`
- **Closing Contract Value**: `Lot × Closing Price × Contract Size × Multiplication Factor`
- **Profit/Loss**: `Closing Contract Value - Contract Value`

### Contract Size Mapping
- XAU (Gold): 5000
- XAG (Silver): 5000  
- LCU (Copper): 25
- AL (Aluminum): 25
- And more...

### Multiplication Factors
- XCEC (COMEX): 0.01
- XLME (LME): 1.00
- Default: 1.00

## 📊 Console Output Structure

The generated Console has 34 columns in two main sections:

**Main Data (Columns 1-16):**
- Date, Account, Contract, Expiry, Price, Lot
- Contract Size, Multiplication Factor, Contract Value
- Commission, Input Source, Venue
- Closing Price, Closing Contract Value, Profit/Loss, Data Type

**Duplicate Data (Columns 17-31):**
- Same fields with .1 suffix for reconciliation purposes

**Rev Fields (Columns 32-34):**
- Rev Price, Rev Qty, Rev Value

## 🛠️ Customization

### Adding New Contract Types
Edit the `contract_sizes` dictionary in `ConsoleGenerator` class:

```python
self.contract_sizes = {
    'XAU': 5000,  # Gold
    'NEW_CONTRACT': 100,  # Your new contract
    # ...
}
```

### Adding New Venues
Edit the `multiplication_factors` dictionary:

```python
self.multiplication_factors = {
    'XCEC': 0.01,  # COMEX
    'NEW_VENUE': 0.5,  # Your new venue
    # ...
}
```

### Column Mapping
Modify the mapping dictionaries in `DataParser` class to handle new file formats.

## 📈 Example Usage

```python
from reconciliation_system import ReconciliationSystem

# Initialize system
system = ReconciliationSystem()

# Process files
console_df = system.process_files(
    broker_file_path="Broker File.xlsx",
    trader_file_paths=["Trader1.xlsx", "Trader2.xlsx"]
)

# Save result
system.save_console(console_df, "My_Console_Output.xlsx")
```

## 🔍 Troubleshooting

### Common Issues

1. **"No data found in trader file"**
   - Check if file has the expected structure
   - Verify column headers are present
   - Ensure data rows exist below headers

2. **"Error parsing broker file"**
   - Verify file is a valid Excel file
   - Check column names match expected format
   - Ensure file is not corrupted

3. **Missing calculations**
   - Check if contract type is in the mapping
   - Verify venue/exchange is recognized
   - Ensure numeric fields are properly formatted

### Debug Mode
Add debug prints by modifying the parsing functions to see detailed processing steps.

## 📝 Requirements

- Python 3.7+
- pandas >= 1.5.0
- numpy >= 1.21.0
- openpyxl >= 3.0.0
- streamlit >= 1.28.0 (for web interface)
- python-dateutil >= 2.8.0

## 🤝 Contributing

To extend the system:
1. Add new file format parsers in `DataParser` class
2. Update column mappings for new data sources
3. Add new calculation logic in `ConsoleGenerator` class
4. Test with sample files

## 📄 License

This system is designed for internal reconciliation automation. Modify as needed for your specific requirements.
