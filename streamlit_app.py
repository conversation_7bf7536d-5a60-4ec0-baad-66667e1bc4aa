"""
Streamlit Web Interface for Reconciliation System
Provides a user-friendly web interface for uploading files and generating Console reconciliation.
"""

import streamlit as st
import pandas as pd
import tempfile
import os
from datetime import datetime
import io
from reconciliation_system import ReconciliationSystem

# Page configuration
st.set_page_config(
    page_title="Reconciliation Automation System",
    page_icon="📊",
    layout="wide"
)

def main():
    st.title("📊 Reconciliation Automation System")
    st.markdown("---")
    
    st.markdown("""
    ### 🎯 Purpose
    This system automates the creation of Console reconciliation sheets from trader and broker Excel files.

    ### 🧠 Smart Learning System
    - **Automatically learns** contract sizes and multiplication factors from your data
    - **Adapts to new instruments** and venues without manual configuration
    - **Saves learned values** for future use in `learned_config.json`

    ### 📁 Upload Requirements
    - **1 Broker File**: Excel file containing broker trade data
    - **1+ Trader Files**: Excel files containing trader data (Star, CQG, or other formats)

    ### 📋 Process
    1. Upload your broker file
    2. Upload one or more trader files
    3. Click "Generate Console" to process
    4. Download the generated Console reconciliation file
    """)
    
    st.markdown("---")
    
    # File upload section
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("📄 Broker File")
        broker_file = st.file_uploader(
            "Upload Broker Excel File",
            type=['xlsx', 'xls'],
            key="broker_upload",
            help="Upload the broker file (e.g., Broker File.xlsx)"
        )
        
        if broker_file:
            st.success(f"✅ Broker file uploaded: {broker_file.name}")
    
    with col2:
        st.subheader("📄 Trader Files")
        trader_files = st.file_uploader(
            "Upload Trader Excel Files",
            type=['xlsx', 'xls'],
            accept_multiple_files=True,
            key="trader_upload",
            help="Upload one or more trader files (Star, CQG, etc.)"
        )
        
        if trader_files:
            st.success(f"✅ {len(trader_files)} trader file(s) uploaded:")
            for file in trader_files:
                st.write(f"  • {file.name}")
    
    st.markdown("---")
    
    # Processing section
    if broker_file and trader_files:
        col1, col2, col3 = st.columns([1, 2, 1])
        
        with col2:
            if st.button("🚀 Generate Console Reconciliation", type="primary", use_container_width=True):
                generate_console(broker_file, trader_files)
    else:
        st.info("📋 Please upload both broker and trader files to proceed.")
    
    # Information section
    st.markdown("---")
    st.subheader("ℹ️ System Information")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("""
        **Supported File Formats:**
        - Excel files (.xlsx, .xls)
        - Star trader file format
        - CQG trader file format
        - Standard broker file format
        """)
    
    with col2:
        st.markdown("""
        **Generated Console Includes:**
        - 34 standardized columns
        - Calculated contract values
        - Profit/loss calculations
        - Commission data
        - Closing prices and values
        """)


def generate_console(broker_file, trader_files):
    """Generate Console reconciliation from uploaded files."""
    
    with st.spinner("🔄 Processing files and generating Console..."):
        try:
            # Create temporary directory for processing
            with tempfile.TemporaryDirectory() as temp_dir:
                
                # Save broker file
                broker_path = os.path.join(temp_dir, broker_file.name)
                with open(broker_path, 'wb') as f:
                    f.write(broker_file.getvalue())
                
                # Save trader files
                trader_paths = []
                for trader_file in trader_files:
                    trader_path = os.path.join(temp_dir, trader_file.name)
                    with open(trader_path, 'wb') as f:
                        f.write(trader_file.getvalue())
                    trader_paths.append(trader_path)
                
                # Initialize reconciliation system with learning enabled
                system = ReconciliationSystem(learn_from_reference=True)

                # Process files
                console_df = system.process_files(broker_path, trader_paths)

                if not console_df.empty:
                    st.success("✅ Console reconciliation generated successfully!")

                    # Show learned configuration
                    with st.expander("🧠 Learned Configuration Values", expanded=False):
                        col1, col2 = st.columns(2)

                        with col1:
                            st.subheader("📊 Contract Sizes")
                            for contract, size in system.generator.contract_sizes.items():
                                st.write(f"**{contract}**: {size:,.0f}")

                        with col2:
                            st.subheader("🏢 Multiplication Factors")
                            for venue, factor in system.generator.multiplication_factors.items():
                                st.write(f"**{venue}**: {factor}")

                        st.info("💡 These values were automatically learned from your data and saved to `learned_config.json` for future use.")

                if not console_df.empty:
                    st.success("✅ Console reconciliation generated successfully!")
                    
                    # Display summary
                    col1, col2, col3 = st.columns(3)
                    
                    with col1:
                        st.metric("Total Records", len(console_df))
                    
                    with col2:
                        broker_records = len(console_df[console_df['Data Type'] == 'Broker'])
                        st.metric("Broker Records", broker_records)
                    
                    with col3:
                        trader_records = len(console_df[console_df['Data Type'] == 'Trader'])
                        st.metric("Trader Records", trader_records)
                    
                    # Display preview
                    st.subheader("📋 Console Preview")
                    st.dataframe(console_df.head(10), use_container_width=True)
                    
                    # Download section
                    st.subheader("💾 Download Console")
                    
                    # Create Excel file in memory
                    output = io.BytesIO()
                    with pd.ExcelWriter(output, engine='openpyxl') as writer:
                        console_df.to_excel(writer, sheet_name='Console', index=False)
                    
                    # Generate filename with timestamp
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    filename = f"Console_Reconciliation_{timestamp}.xlsx"
                    
                    st.download_button(
                        label="📥 Download Console Excel File",
                        data=output.getvalue(),
                        file_name=filename,
                        mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                        type="primary",
                        use_container_width=True
                    )
                    
                    # Additional analysis
                    if st.checkbox("📊 Show Detailed Analysis"):
                        show_analysis(console_df)
                
                else:
                    st.error("❌ No data could be processed from the uploaded files.")
                    st.info("Please check that your files contain valid trade data.")
        
        except Exception as e:
            st.error(f"❌ Error processing files: {str(e)}")
            st.info("Please check your file formats and try again.")


def show_analysis(console_df):
    """Show detailed analysis of the Console data."""
    
    st.subheader("📈 Detailed Analysis")
    
    # Summary statistics
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("**Contract Summary:**")
        if 'Contract' in console_df.columns:
            contract_counts = console_df['Contract'].value_counts()
            st.dataframe(contract_counts.head(10))
    
    with col2:
        st.markdown("**Venue Summary:**")
        if 'Venue' in console_df.columns:
            venue_counts = console_df['Venue'].value_counts()
            st.dataframe(venue_counts)
    
    # Financial summary
    st.markdown("**Financial Summary:**")
    financial_cols = ['Contract Value', 'Commission', 'Closing Contract Value', 'profitloss']
    
    summary_data = {}
    for col in financial_cols:
        if col in console_df.columns:
            summary_data[col] = {
                'Total': console_df[col].sum(),
                'Average': console_df[col].mean(),
                'Min': console_df[col].min(),
                'Max': console_df[col].max()
            }
    
    if summary_data:
        summary_df = pd.DataFrame(summary_data).T
        st.dataframe(summary_df, use_container_width=True)


if __name__ == "__main__":
    main()
